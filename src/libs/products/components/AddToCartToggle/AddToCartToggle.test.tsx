import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AddToCartToggle } from './AddToCartToggle';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { useCartProductMapState } from '@/libs/cart/hooks/useCartProductMapState';

// Mock the hooks
jest.mock('@/apps/shop/stores/useCartStore/useCartStore');
jest.mock('@/libs/cart/hooks/useCartProductMapState');

const mockAddToCart = jest.fn();
const mockUseCartStore = useCartStore as jest.MockedFunction<typeof useCartStore>;
const mockUseCartProductMapState = useCartProductMapState as jest.MockedFunction<typeof useCartProductMapState>;

describe('AddToCartToggle', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    mockUseCartStore.mockReturnValue({
      addToCart: mockAddToCart,
      updatingProductIds: new Set(),
      // Add other required properties with default values
      budget: null,
      vendors: [],
      isCartLoading: false,
      errorOnCartLoading: false,
      itemsCount: 0,
      uniqueItemsCount: 0,
      subtotal: '0',
      total: '0',
      fetchCart: jest.fn(),
      clearCart: jest.fn(),
      setProductUpdating: jest.fn(),
      swapOfferCartItem: jest.fn(),
      checkout: jest.fn(),
    });
  });

  it('renders AddToCartButton when item is not in cart', () => {
    mockUseCartProductMapState.mockReturnValue({});

    render(
      <AddToCartToggle
        productOfferId="test-product"
        minIncrement={1}
      />
    );

    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(screen.queryByDisplayValue('0')).not.toBeInTheDocument();
  });

  it('renders quantity input with minus button when quantity is above minIncrement', () => {
    mockUseCartProductMapState.mockReturnValue({
      'test-product': { quantity: 5 }
    });

    render(
      <AddToCartToggle
        productOfferId="test-product"
        minIncrement={1}
      />
    );

    expect(screen.getByDisplayValue('5')).toBeInTheDocument();
    expect(screen.getByLabelText('Decrease quantity')).toBeInTheDocument();
    expect(screen.getByLabelText('Increase quantity')).toBeInTheDocument();
    expect(screen.queryByLabelText('Remove item from cart')).not.toBeInTheDocument();
  });

  it('renders quantity input with trash button when quantity equals minIncrement', () => {
    mockUseCartProductMapState.mockReturnValue({
      'test-product': { quantity: 1 }
    });

    render(
      <AddToCartToggle
        productOfferId="test-product"
        minIncrement={1}
      />
    );

    expect(screen.getByDisplayValue('1')).toBeInTheDocument();
    expect(screen.getByLabelText('Remove item from cart')).toBeInTheDocument();
    expect(screen.getByLabelText('Increase quantity')).toBeInTheDocument();
    expect(screen.queryByLabelText('Decrease quantity')).not.toBeInTheDocument();
  });

  it('calls addToCart with correct quantity when initial add button is clicked', () => {
    mockUseCartProductMapState.mockReturnValue({});

    render(
      <AddToCartToggle
        productOfferId="test-product"
        minIncrement={5}
      />
    );

    fireEvent.click(screen.getByRole('button'));

    expect(mockAddToCart).toHaveBeenCalledWith({
      offers: [
        {
          productOfferId: 'test-product',
          quantity: 5, // Should use minIncrement when it's greater than 1
        },
      ],
      onError: expect.any(Function),
    });
  });

  it('calls addToCart with 0 quantity when trash button is clicked', () => {
    mockUseCartProductMapState.mockReturnValue({
      'test-product': { quantity: 5 }
    });

    render(
      <AddToCartToggle
        productOfferId="test-product"
        minIncrement={1}
      />
    );

    fireEvent.click(screen.getByLabelText('Remove item from cart'));

    expect(mockAddToCart).toHaveBeenCalledWith({
      offers: [
        {
          productOfferId: 'test-product',
          quantity: 0,
        },
      ],
      onError: expect.any(Function),
    });
  });

  it('calls addToCart with incremented quantity when plus button is clicked', () => {
    mockUseCartProductMapState.mockReturnValue({
      'test-product': { quantity: 5 }
    });

    render(
      <AddToCartToggle
        productOfferId="test-product"
        minIncrement={2}
      />
    );

    fireEvent.click(screen.getByLabelText('Increase quantity'));

    expect(mockAddToCart).toHaveBeenCalledWith({
      offers: [
        {
          productOfferId: 'test-product',
          quantity: 7, // 5 + 2 (minIncrement)
        },
      ],
      onError: expect.any(Function),
    });
  });

  it('calls addToCart with decremented quantity when minus button is clicked', () => {
    mockUseCartProductMapState.mockReturnValue({
      'test-product': { quantity: 10 }
    });

    render(
      <AddToCartToggle
        productOfferId="test-product"
        minIncrement={3}
      />
    );

    fireEvent.click(screen.getByLabelText('Decrease quantity'));

    expect(mockAddToCart).toHaveBeenCalledWith({
      offers: [
        {
          productOfferId: 'test-product',
          quantity: 7, // 10 - 3 (minIncrement)
        },
      ],
      onError: expect.any(Function),
    });
  });

  it('shows loading state when product is being updated', () => {
    mockUseCartStore.mockReturnValue({
      addToCart: mockAddToCart,
      updatingProductIds: new Set(['test-product']),
      // Add other required properties with default values
      budget: null,
      vendors: [],
      isCartLoading: false,
      errorOnCartLoading: false,
      itemsCount: 0,
      uniqueItemsCount: 0,
      subtotal: '0',
      total: '0',
      fetchCart: jest.fn(),
      clearCart: jest.fn(),
      setProductUpdating: jest.fn(),
      swapOfferCartItem: jest.fn(),
      checkout: jest.fn(),
    });

    mockUseCartProductMapState.mockReturnValue({
      'test-product': { quantity: 5 }
    });

    render(
      <AddToCartToggle
        productOfferId="test-product"
        minIncrement={1}
      />
    );

    // Should show spinner instead of input when updating
    expect(screen.queryByDisplayValue('5')).not.toBeInTheDocument();
    // Buttons should be disabled
    expect(screen.getByLabelText('Remove item from cart')).toBeDisabled();
    expect(screen.getByLabelText('Increase quantity')).toBeDisabled();
  });

  it('adjusts input value to lower allowed increment when user enters invalid value', () => {
    mockUseCartProductMapState.mockReturnValue({
      'test-product': { quantity: 5 }
    });

    render(
      <AddToCartToggle
        productOfferId="test-product"
        minIncrement={3}
      />
    );

    const input = screen.getByDisplayValue('5');

    // User enters 7, which should be adjusted to 6 (lower allowed increment)
    fireEvent.change(input, { target: { value: '7' } });
    fireEvent.blur(input);

    expect(mockAddToCart).toHaveBeenCalledWith({
      offers: [
        {
          productOfferId: 'test-product',
          quantity: 6, // 7 adjusted down to 6 (Math.floor(7/3) * 3 = 6)
        },
      ],
      onError: expect.any(Function),
    });
  });

  it('adjusts input value to minimum increment when user enters value less than minIncrement', () => {
    mockUseCartProductMapState.mockReturnValue({
      'test-product': { quantity: 10 }
    });

    render(
      <AddToCartToggle
        productOfferId="test-product"
        minIncrement={5}
      />
    );

    const input = screen.getByDisplayValue('10');

    // User enters 2, which should be adjusted to 5 (minimum increment)
    fireEvent.change(input, { target: { value: '2' } });
    fireEvent.blur(input);

    expect(mockAddToCart).toHaveBeenCalledWith({
      offers: [
        {
          productOfferId: 'test-product',
          quantity: 5, // 2 adjusted to minimum increment of 5
        },
      ],
      onError: expect.any(Function),
    });
  });

  it('shows trash button when quantity equals minIncrement with higher increment value', () => {
    mockUseCartProductMapState.mockReturnValue({
      'test-product': { quantity: 5 }
    });

    render(
      <AddToCartToggle
        productOfferId="test-product"
        minIncrement={5}
      />
    );

    expect(screen.getByDisplayValue('5')).toBeInTheDocument();
    expect(screen.getByLabelText('Remove item from cart')).toBeInTheDocument();
    expect(screen.getByLabelText('Increase quantity')).toBeInTheDocument();
    expect(screen.queryByLabelText('Decrease quantity')).not.toBeInTheDocument();
  });
});
