# AddToCartToggle Component

A React component that combines the functionality of AddToCartButton and AddToCartInput with immediate cart updates. This component provides a seamless user experience for adding items to cart and managing quantities.

## Features

- **Initial State**: Shows an AddToCartButton when item is not in cart
- **Transition State**: Shows loading spinner during cart updates
- **Item in Cart State**: Shows editable quantity input with trash and plus buttons
- **Immediate Cart Updates**: All quantity changes immediately call the cart API
- **Input Validation**: Respects minIncrement constraints and validates user input
- **Accessibility**: Proper ARIA labels and keyboard navigation support

## Props

```typescript
interface AddToCartToggleProps {
  productOfferId: string;  // Unique identifier for the product offer
  minIncrement: number;    // Minimum increment for quantity changes
  size?: 'sm' | 'md' | 'lg';  // Size of the component (default: 'md')
  className?: string;      // Additional CSS classes
}
```

## Usage

```tsx
import { AddToCartToggle } from '@/libs/products/components/AddToCartToggle';

// Basic usage
<AddToCartToggle
  productOfferId="product-123"
  minIncrement={1}
/>

// With custom size and styling
<AddToCartToggle
  productOfferId="product-456"
  minIncrement={5}
  size="lg"
  className="custom-class"
/>
```

## States

### 1. Initial State (Item NOT in cart)
- Displays AddToCartButton component
- When clicked, adds `Math.max(1, minIncrement)` quantity to cart
- Shows loading state during API call

### 2. Item in Cart State
- Displays custom input layout with:
  - **Left button**:
    - **Trash icon**: Shows when quantity ≤ minIncrement (removes item completely, quantity: 0)
    - **Minus icon**: Shows when quantity > minIncrement (decrements by minIncrement amount)
  - **Quantity input** (center): Editable number input
  - **Plus button** (right): Increments by minIncrement amount

### 3. Loading State
- Shows spinner in place of quantity input
- Disables all interactive elements
- Maintains layout structure

## Behavior

### Left Button Logic
The left button changes based on the current quantity:
- **Trash Icon**: Displayed when `currentQuantity <= minIncrement`
  - This means you cannot decrement further without removing the item entirely
  - Examples: quantity=1 with minIncrement=1, or quantity=5 with minIncrement=5
- **Minus Icon**: Displayed when `currentQuantity > minIncrement`
  - Allows decrementing by minIncrement amount
  - Examples: quantity=10 with minIncrement=5 (can decrement to 5)

### Quantity Validation
- Input values are validated against minIncrement using the same logic as AddToCartInput
- Invalid inputs are adjusted to the lower allowed increment (Math.floor(value / minIncrement) * minIncrement)
- Values less than minIncrement are adjusted to minIncrement (unless zero for removal)
- Minimum quantity is minIncrement (cannot go below except when removing)
- Input field value is automatically updated to show the corrected value

### Cart Updates
- Every change immediately calls `addToCart()` API
- Follows CartVendorProductItem pattern for immediate updates
- Handles API errors with user-friendly messages
- Uses `useCartStore` hook for cart operations

### Keyboard Support
- Enter key in input field triggers blur (saves value)
- Tab navigation between interactive elements
- Proper focus management

## Dependencies

- `@/libs/ui/AddToCartButton` - Initial add to cart button
- `@/libs/form/Input` - Quantity input field
- `@/libs/icons/Icon` - Trash, minus, plus, and clock icons
- `@/apps/shop/stores/useCartStore` - Cart state management
- `@/libs/cart/hooks/useCartProductMapState` - Current cart quantities
- `@mantine/core` - Popover component for increment info
- **Tailwind CSS** - All styling and animations

## Styling

The component uses Tailwind CSS classes for styling:
- **Positioned buttons**: `absolute top-1/2 left-2/right-2 z-10 flex h-8 -translate-y-1/2 items-center`
- **Loading container**: `relative flex h-9 min-w-16 items-center justify-center rounded border border-gray-300 bg-gray-50`
- **Loading spinner**: `flex animate-spin items-center justify-center`
- **Input padding**: `px-2.5`
- **Container**: `relative flex items-center`

## Testing

The component includes comprehensive tests covering:
- Initial state rendering
- State transitions
- User interactions
- Loading states
- Error handling
- Accessibility features

Run tests with:
```bash
npm test AddToCartToggle
```

## Accessibility

- All interactive elements have proper ARIA labels
- Keyboard navigation support
- Screen reader friendly
- Focus management during state changes
- Error messages are announced to screen readers
