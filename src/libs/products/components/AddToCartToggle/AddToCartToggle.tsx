import React, { useState, useCallback } from 'react';
import { Popover } from '@mantine/core';
import { Button } from '@/libs/ui/Button/Button';
import { Input, InputProps } from '@/libs/form/Input';
import { Icon } from '@/libs/icons/Icon';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { useCartProductMapState } from '@/libs/cart/hooks/useCartProductMapState';
import { AddToCartButton } from '@/libs/ui/AddToCartButton/AddToCartButton';

export interface AddToCartToggleProps {
  productOfferId: string;
  minIncrement: number;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const AddToCartToggle = ({
  productOfferId,
  minIncrement,
  size = 'md',
  className = '',
}: AddToCartToggleProps) => {
  const { addToCart, updatingProductIds } = useCartStore();
  const cartProductMapState = useCartProductMapState();
  const [error, setError] = useState('');
  const [showIncrementInfo, setShowIncrementInfo] = useState(false);

  const currentQuantity = cartProductMapState[productOfferId]?.quantity ?? 0;
  const isUpdating = updatingProductIds.has(productOfferId);
  const hasItemsInCart = currentQuantity > 0;

  // Show trash icon only when quantity reaches the minimum removable amount
  const showTrashIcon = currentQuantity <= minIncrement;

  const handleAddToCart = useCallback(
    (quantity: number) => {
      addToCart({
        offers: [
          {
            productOfferId,
            quantity,
          },
        ],
        onError: (message) => {
          setError(message);
        },
      });
    },
    [productOfferId, addToCart],
  );

  const handleInitialAddToCart = useCallback(() => {
    const initialQuantity = Math.max(1, minIncrement);
    handleAddToCart(initialQuantity);
  }, [minIncrement, handleAddToCart]);

  const handleQuantityUpdate = useCallback(
    (newQuantity: number) => {
      let validQuantity = newQuantity;

      // Handle zero or negative values
      if (validQuantity <= 0) {
        validQuantity = 0;
      } else if (validQuantity % minIncrement !== 0) {
        // Use same logic as AddToCartInput: floor to lower allowed increment
        validQuantity =
          (Math.floor(validQuantity / minIncrement) || 1) * minIncrement;
      }

      setError('');
      handleAddToCart(validQuantity);
    },
    [minIncrement, handleAddToCart],
  );

  const handleInputBlur = useCallback(
    (event: React.FocusEvent<HTMLInputElement>) => {
      const inputElement = event.target;
      let newValue = parseInt(inputElement.value, 10);

      // Handle invalid input
      if (isNaN(newValue) || newValue < 0) {
        inputElement.value = currentQuantity.toString();
        setError('Please enter a valid quantity');
        return;
      }

      // Handle zero values
      if (newValue <= 0) {
        handleQuantityUpdate(0);
        return;
      }

      // Apply same increment validation as AddToCartInput
      if (newValue % minIncrement !== 0) {
        newValue = (Math.floor(newValue / minIncrement) || 1) * minIncrement;
        inputElement.value = newValue.toString();
      }

      handleQuantityUpdate(newValue);
    },
    [currentQuantity, handleQuantityUpdate, minIncrement],
  );

  const handleInputKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.code === 'Enter') {
        event.currentTarget.blur();
      }
    },
    [],
  );

  const handleIncrement = useCallback(() => {
    const newQuantity = currentQuantity + minIncrement;
    handleQuantityUpdate(newQuantity);
  }, [currentQuantity, minIncrement, handleQuantityUpdate]);

  const handleRemove = useCallback(() => {
    handleAddToCart(0);
  }, [handleAddToCart]);

  const handleDecrement = useCallback(() => {
    const newQuantity = currentQuantity - minIncrement;
    handleQuantityUpdate(newQuantity);
  }, [currentQuantity, minIncrement, handleQuantityUpdate]);

  // Convert size prop to InputProps size
  const inputSize: InputProps['size'] =
    size === 'lg' ? 'lg' : size === 'sm' ? 'sm' : 'md';

  if (!hasItemsInCart) {
    return (
      <div className={className}>
        <AddToCartButton
          isLoading={isUpdating}
          onClick={handleInitialAddToCart}
          height={size === 'sm' ? 'sm' : 'normal'}
        />
      </div>
    );
  }

  // Item in cart state: show custom input with trash and plus buttons
  return (
    <div className={className}>
      <Popover opened={showIncrementInfo}>
        <Popover.Target>
          <div className="relative flex items-center">
            {/* Trash/Remove button or Minus button on the left */}
            {showTrashIcon ? (
              <Button
                aria-label="Remove item from cart"
                type="button"
                variant="unstyled"
                className="absolute top-1/2 left-2 z-10 flex h-8 -translate-y-1/2 items-center"
                onClick={handleRemove}
                disabled={isUpdating}
              >
                <Icon name="trash" aria-hidden={true} />
              </Button>
            ) : (
              <Button
                aria-label="Decrease quantity"
                type="button"
                variant="unstyled"
                className="absolute top-1/2 left-2 z-10 flex h-8 -translate-y-1/2 items-center"
                onClick={handleDecrement}
                disabled={isUpdating}
              >
                <Icon name="minus" aria-hidden={true} />
              </Button>
            )}

            {/* Quantity input in the center */}
            {isUpdating ? (
              <div className="relative flex h-9 min-w-16 items-center justify-center rounded border border-gray-300 bg-gray-50">
                <div className="flex animate-spin items-center justify-center">
                  <Icon name="clock" size="1rem" />
                </div>
              </div>
            ) : (
              <Input
                defaultValue={currentQuantity}
                key={currentQuantity} // Force re-render when quantity changes
                align="center"
                onKeyDown={handleInputKeyDown}
                onBlur={handleInputBlur}
                onFocus={() => setShowIncrementInfo(minIncrement > 1)}
                error={error}
                type="number"
                min="0"
                step={minIncrement}
                size={inputSize}
                className="px-2.5"
              />
            )}

            {/* Plus button on the right */}
            <Button
              aria-label="Increase quantity"
              type="button"
              variant="unstyled"
              className="absolute top-1/2 right-2 z-10 flex h-8 -translate-y-1/2 items-center"
              onClick={handleIncrement}
              disabled={isUpdating}
            >
              <Icon name="plus" aria-hidden={true} />
            </Button>
          </div>
        </Popover.Target>

        <Popover.Dropdown>
          This product requires increments of {minIncrement}
        </Popover.Dropdown>
      </Popover>
    </div>
  );
};
